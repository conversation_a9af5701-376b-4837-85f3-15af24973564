/**
 * <PERSON><PERSON>io Principal de Chunking de Documentos
 * 
 * Este servicio orquesta el proceso completo de división inteligente de documentos
 * en chunks para procesamiento por IA, siguiendo los patrones arquitectónicos
 * establecidos en ARCHITECTURE.md.
 */

import { 
  dividirEnChunks, 
  obtenerEstadisticasChunking,
  type Chunk,
  type ChunkingConfig 
} from '@/lib/utils/textProcessing';

import {
  type DocumentChunkingResult,
  type DocumentProcessingOptions,
  type ChunkingStats,
  type ChunkingProgress,
  ChunkingError,
  ChunkingErrorCode
} from '@/types/chunking';

import { 
  getChunkingConfigForContentType,
  validateChunkingConfig,
  CHUNKING_LIMITS,
  CHUNKING_LOGGING_CONFIG 
} from '@/config/chunking';

import { type Documento } from '@/types/database';

/**
 * Servicio principal para el chunking de documentos
 */
export class DocumentChunkingService {
  private static readonly logger = CHUNKING_LOGGING_CONFIG.customLogger || console.log;

  /**
   * Procesa un documento y determina si necesita chunking
   * 
   * @param documento - Documento a procesar
   * @param options - Opciones de procesamiento
   * @returns Resultado del procesamiento (string simple o array de chunks)
   */
  static processDocument(
    documento: Documento, 
    options: DocumentProcessingOptions = { enableChunking: true }
  ): DocumentChunkingResult {
    const startTime = Date.now();
    
    try {
      // Validar entrada
      this.validateInput(documento, options);
      
      // Obtener configuración de chunking
      const config = this.getEffectiveConfig(options);
      
      // Determinar si necesita chunking
      const needsChunking = this.shouldChunkDocument(documento, options, config);
      
      if (!needsChunking) {
        return this.createSimpleResult(documento, config, startTime);
      }
      
      // Realizar chunking
      return this.performChunking(documento, config, options, startTime);
      
    } catch (error) {
      this.logger('error', 'Error en processDocument', { 
        documentId: documento.id,
        error: error instanceof Error ? error.message : String(error)
      });
      
      if (error instanceof ChunkingError) {
        throw error;
      }
      
      throw new ChunkingError(
        `Error inesperado al procesar documento: ${error instanceof Error ? error.message : String(error)}`,
        ChunkingErrorCode.UNKNOWN_ERROR,
        { originalError: error }
      );
    }
  }

  /**
   * Procesa múltiples documentos
   */
  static processMultipleDocuments(
    documentos: Documento[],
    options: DocumentProcessingOptions = { enableChunking: true }
  ): DocumentChunkingResult[] {
    this.logger('info', `Procesando ${documentos.length} documentos`);
    
    return documentos.map(documento => {
      try {
        return this.processDocument(documento, options);
      } catch (error) {
        this.logger('error', 'Error procesando documento', {
          documentId: documento.id,
          titulo: documento.titulo,
          error: error instanceof Error ? error.message : String(error)
        });
        
        // Retornar resultado de fallback para este documento
        return this.createErrorResult(documento, error);
      }
    });
  }

  /**
   * Valida la entrada del servicio
   */
  private static validateInput(documento: Documento, options: DocumentProcessingOptions): void {
    if (!documento) {
      throw new ChunkingError(
        'Documento no proporcionado',
        ChunkingErrorCode.INVALID_CONTENT
      );
    }

    if (!documento.contenido || typeof documento.contenido !== 'string') {
      throw new ChunkingError(
        'El documento debe tener contenido válido',
        ChunkingErrorCode.INVALID_CONTENT,
        { documentId: documento.id, titulo: documento.titulo }
      );
    }

    if (documento.contenido.length > CHUNKING_LIMITS.MAX_DOCUMENT_SIZE) {
      throw new ChunkingError(
        `Documento demasiado grande: ${documento.contenido.length} caracteres (máximo: ${CHUNKING_LIMITS.MAX_DOCUMENT_SIZE})`,
        ChunkingErrorCode.CHUNK_TOO_LARGE,
        { documentId: documento.id, size: documento.contenido.length }
      );
    }

    // Validar configuración personalizada si se proporciona
    if (options.chunkingConfig) {
      const validation = validateChunkingConfig(options.chunkingConfig);
      if (!validation.isValid) {
        throw new ChunkingError(
          `Configuración de chunking inválida: ${validation.errors.join(', ')}`,
          ChunkingErrorCode.CONFIG_ERROR,
          { errors: validation.errors, warnings: validation.warnings }
        );
      }
    }
  }

  /**
   * Obtiene la configuración efectiva de chunking
   */
  private static getEffectiveConfig(options: DocumentProcessingOptions): ChunkingConfig {
    return getChunkingConfigForContentType(
      options.contentType,
      options.chunkingConfig
    );
  }

  /**
   * Determina si un documento necesita chunking
   */
  private static shouldChunkDocument(
    documento: Documento,
    options: DocumentProcessingOptions,
    config: ChunkingConfig
  ): boolean {
    // Si el chunking está deshabilitado
    if (!options.enableChunking) {
      return false;
    }

    // Si se fuerza el chunking
    if (options.forceChunking) {
      return true;
    }

    // Si el documento es pequeño, no necesita chunking
    if (documento.contenido.length <= Math.max(config.maxChunkSize, CHUNKING_LIMITS.MIN_SIZE_FOR_CHUNKING)) {
      return false;
    }

    return true;
  }

  /**
   * Crea un resultado simple para documentos que no necesitan chunking
   */
  private static createSimpleResult(
    documento: Documento,
    config: ChunkingConfig,
    startTime: number
  ): DocumentChunkingResult {
    const processingTime = Date.now() - startTime;
    
    return {
      wasChunked: false,
      content: documento.contenido,
      stats: {
        totalChunks: 1,
        averageChunkSize: documento.contenido.length,
        minChunkSize: documento.contenido.length,
        maxChunkSize: documento.contenido.length,
        totalContentSize: documento.contenido.length,
        sectionsDetected: 0,
        chunkingStrategy: 'none',
        processingTimeMs: processingTime
      },
      configUsed: config,
      sourceDocument: {
        id: documento.id,
        title: documento.titulo,
        originalSize: documento.contenido.length
      }
    };
  }

  /**
   * Realiza el chunking del documento
   */
  private static performChunking(
    documento: Documento,
    config: ChunkingConfig,
    options: DocumentProcessingOptions,
    startTime: number
  ): DocumentChunkingResult {
    this.logger('info', 'Iniciando chunking de documento', {
      documentId: documento.id,
      titulo: documento.titulo,
      size: documento.contenido.length,
      config
    });

    // Reportar progreso
    this.reportProgress(options, {
      phase: 'detecting_sections',
      percentage: 10,
      message: 'Detectando secciones del documento',
      chunksProcessed: 0,
      estimatedTotalChunks: 0
    });

    // Realizar chunking
    const chunks = dividirEnChunks(documento.contenido, config, documento.id);
    
    if (chunks.length > CHUNKING_LIMITS.MAX_CHUNKS_PER_DOCUMENT) {
      throw new ChunkingError(
        `Demasiados chunks generados: ${chunks.length} (máximo: ${CHUNKING_LIMITS.MAX_CHUNKS_PER_DOCUMENT})`,
        ChunkingErrorCode.CHUNK_TOO_LARGE,
        { documentId: documento.id, chunksGenerated: chunks.length }
      );
    }

    // Reportar progreso final
    this.reportProgress(options, {
      phase: 'finalizing',
      percentage: 100,
      message: 'Chunking completado',
      chunksProcessed: chunks.length,
      estimatedTotalChunks: chunks.length
    });

    const processingTime = Date.now() - startTime;
    const stats = this.createChunkingStats(chunks, processingTime);

    this.logger('info', 'Chunking completado exitosamente', {
      documentId: documento.id,
      stats
    });

    return {
      wasChunked: true,
      content: chunks,
      stats,
      configUsed: config,
      sourceDocument: {
        id: documento.id,
        title: documento.titulo,
        originalSize: documento.contenido.length
      }
    };
  }

  /**
   * Crea estadísticas de chunking
   */
  private static createChunkingStats(chunks: Chunk[], processingTime: number): ChunkingStats {
    const basicStats = obtenerEstadisticasChunking(chunks);
    const firstChunk = chunks[0];
    
    return {
      totalChunks: basicStats.totalChunks,
      averageChunkSize: basicStats.averageSize,
      minChunkSize: basicStats.minSize,
      maxChunkSize: basicStats.maxSize,
      totalContentSize: basicStats.totalSize,
      sectionsDetected: basicStats.sectionsDetected,
      chunkingStrategy: firstChunk?.metadata.chunkType || 'section',
      processingTimeMs: processingTime
    };
  }

  /**
   * Reporta progreso del chunking
   */
  private static reportProgress(
    options: DocumentProcessingOptions,
    progress: ChunkingProgress
  ): void {
    if (options.onProgress) {
      options.onProgress(progress);
    }
  }

  /**
   * Crea un resultado de error para documentos que fallaron
   */
  private static createErrorResult(documento: Documento, error: any): DocumentChunkingResult {
    return {
      wasChunked: false,
      content: documento.contenido.substring(0, 15000), // Fallback al truncamiento original
      stats: {
        totalChunks: 1,
        averageChunkSize: 15000,
        minChunkSize: 15000,
        maxChunkSize: 15000,
        totalContentSize: documento.contenido.length,
        sectionsDetected: 0,
        chunkingStrategy: 'none',
        processingTimeMs: 0
      },
      configUsed: getChunkingConfigForContentType('default'),
      sourceDocument: {
        id: documento.id,
        title: documento.titulo,
        originalSize: documento.contenido.length
      }
    };
  }

  /**
   * Obtiene métricas de rendimiento del servicio
   */
  static getPerformanceMetrics(): any {
    // TODO: Implementar sistema de métricas
    return {
      message: 'Métricas de rendimiento no implementadas aún'
    };
  }
}
