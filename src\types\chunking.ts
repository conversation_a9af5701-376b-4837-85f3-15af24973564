/**
 * Tipos TypeScript para el Sistema de Chunking Inteligente
 * 
 * Este archivo define todas las interfaces y tipos relacionados con
 * la división inteligente de documentos en chunks para procesamiento por IA.
 */

// Re-exportar tipos básicos desde textProcessing para mantener consistencia
export type {
  Chunk,
  ChunkMetadata,
  ChunkingConfig
} from '@/lib/utils/textProcessing';

/**
 * Resultado del procesamiento de un documento por el sistema de chunking
 */
export interface DocumentChunkingResult {
  /** Indica si el documento fue dividido en chunks */
  wasChunked: boolean;
  
  /** Contenido original o array de chunks */
  content: string | Chunk[];
  
  /** Estadísticas del proceso de chunking */
  stats: ChunkingStats;
  
  /** Configuración utilizada para el chunking */
  configUsed: ChunkingConfig;
  
  /** Información del documento original */
  sourceDocument: {
    id?: string;
    title: string;
    originalSize: number;
  };
}

/**
 * Estadísticas del proceso de chunking
 */
export interface ChunkingStats {
  /** Número total de chunks generados */
  totalChunks: number;
  
  /** Tamaño promedio de los chunks */
  averageChunkSize: number;
  
  /** Tamaño del chunk más pequeño */
  minChunkSize: number;
  
  /** Tamaño del chunk más grande */
  maxChunkSize: number;
  
  /** Tamaño total del contenido */
  totalContentSize: number;
  
  /** Número de secciones detectadas */
  sectionsDetected: number;
  
  /** Tipo de división utilizada */
  chunkingStrategy: 'section' | 'paragraph' | 'sentence' | 'fixed' | 'none';
  
  /** Tiempo de procesamiento en milisegundos */
  processingTimeMs: number;
}

/**
 * Configuración específica por tipo de contenido
 */
export interface ContentTypeChunkingConfig {
  /** Configuración para documentos de temario */
  temario: ChunkingConfig;
  
  /** Configuración para documentos legales */
  legal: ChunkingConfig;
  
  /** Configuración para documentos técnicos */
  tecnico: ChunkingConfig;
  
  /** Configuración por defecto */
  default: ChunkingConfig;
}

/**
 * Contexto de chunking para prompts de IA
 */
export interface ChunkingContext {
  /** Número del chunk actual */
  currentChunk: number;
  
  /** Total de chunks */
  totalChunks: number;
  
  /** Indica si hay chunks anteriores */
  hasPreviousChunks: boolean;
  
  /** Indica si hay chunks posteriores */
  hasNextChunks: boolean;
  
  /** Secciones detectadas en el documento completo */
  documentSections: string[];
  
  /** Tipo de estrategia de chunking utilizada */
  chunkingStrategy: ChunkingStats['chunkingStrategy'];
  
  /** Información adicional para el contexto */
  additionalContext?: string;
}

/**
 * Opciones para el procesamiento de documentos
 */
export interface DocumentProcessingOptions {
  /** Habilitar o deshabilitar chunking */
  enableChunking: boolean;
  
  /** Configuración personalizada de chunking */
  chunkingConfig?: Partial<ChunkingConfig>;
  
  /** Tipo de contenido para usar configuración específica */
  contentType?: keyof ContentTypeChunkingConfig;
  
  /** Forzar chunking incluso para documentos pequeños */
  forceChunking?: boolean;
  
  /** Incluir estadísticas detalladas en el resultado */
  includeStats?: boolean;
  
  /** Callback para progreso del chunking */
  onProgress?: (progress: ChunkingProgress) => void;
}

/**
 * Información de progreso durante el chunking
 */
export interface ChunkingProgress {
  /** Fase actual del proceso */
  phase: 'detecting_sections' | 'dividing_content' | 'adding_overlap' | 'finalizing';
  
  /** Porcentaje de completado (0-100) */
  percentage: number;
  
  /** Mensaje descriptivo del progreso */
  message: string;
  
  /** Chunks procesados hasta el momento */
  chunksProcessed: number;
  
  /** Estimación de chunks totales */
  estimatedTotalChunks: number;
}

/**
 * Resultado de la combinación de múltiples chunks procesados por IA
 */
export interface ChunkCombinationResult<T = any> {
  /** Resultado combinado final */
  combinedResult: T;
  
  /** Resultados individuales de cada chunk */
  individualResults: T[];
  
  /** Estadísticas de la combinación */
  combinationStats: {
    /** Número de chunks procesados */
    chunksProcessed: number;
    
    /** Elementos duplicados eliminados */
    duplicatesRemoved: number;
    
    /** Tiempo total de procesamiento */
    totalProcessingTimeMs: number;
    
    /** Estrategia de combinación utilizada */
    combinationStrategy: string;
  };
  
  /** Metadatos adicionales */
  metadata: {
    /** Configuración de chunking utilizada */
    chunkingConfig: ChunkingConfig;
    
    /** Contexto de chunking */
    chunkingContext: ChunkingContext[];
    
    /** Información del documento fuente */
    sourceDocument: {
      id?: string;
      title: string;
      totalSize: number;
    };
  };
}

/**
 * Configuración para la combinación de resultados
 */
export interface ResultCombinationConfig {
  /** Estrategia de combinación */
  strategy: 'merge' | 'deduplicate' | 'prioritize_first' | 'prioritize_last' | 'custom';
  
  /** Función personalizada de combinación */
  customCombiner?: <T>(results: T[]) => T;
  
  /** Configuración para deduplicación */
  deduplication?: {
    /** Función para determinar si dos elementos son duplicados */
    isDuplicate: <T>(a: T, b: T) => boolean;
    
    /** Función para resolver conflictos entre duplicados */
    resolveDuplicate?: <T>(a: T, b: T) => T;
  };
  
  /** Límite máximo de elementos en el resultado final */
  maxResults?: number;
  
  /** Incluir metadatos de combinación */
  includeMetadata?: boolean;
}

/**
 * Error específico del sistema de chunking
 */
export class ChunkingError extends Error {
  constructor(
    message: string,
    public readonly code: ChunkingErrorCode,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'ChunkingError';
  }
}

/**
 * Códigos de error del sistema de chunking
 */
export enum ChunkingErrorCode {
  INVALID_CONTENT = 'INVALID_CONTENT',
  CONFIG_ERROR = 'CONFIG_ERROR',
  SECTION_DETECTION_FAILED = 'SECTION_DETECTION_FAILED',
  CHUNK_TOO_LARGE = 'CHUNK_TOO_LARGE',
  PROCESSING_TIMEOUT = 'PROCESSING_TIMEOUT',
  COMBINATION_FAILED = 'COMBINATION_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Validador de configuración de chunking
 */
export interface ChunkingConfigValidator {
  /** Valida una configuración de chunking */
  validate(config: Partial<ChunkingConfig>): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
  
  /** Normaliza y completa una configuración parcial */
  normalize(config: Partial<ChunkingConfig>): ChunkingConfig;
}

/**
 * Métricas de rendimiento del sistema de chunking
 */
export interface ChunkingPerformanceMetrics {
  /** Tiempo promedio de chunking por documento */
  averageChunkingTime: number;
  
  /** Tiempo promedio de procesamiento por chunk */
  averageProcessingTimePerChunk: number;
  
  /** Número total de documentos procesados */
  totalDocumentsProcessed: number;
  
  /** Número total de chunks generados */
  totalChunksGenerated: number;
  
  /** Distribución de tamaños de chunks */
  chunkSizeDistribution: {
    small: number;    // < 10k caracteres
    medium: number;   // 10k-30k caracteres
    large: number;    // > 30k caracteres
  };
  
  /** Tasa de éxito del chunking */
  successRate: number;
  
  /** Errores más comunes */
  commonErrors: Array<{
    code: ChunkingErrorCode;
    count: number;
    percentage: number;
  }>;
}

/**
 * Configuración de logging para el sistema de chunking
 */
export interface ChunkingLoggingConfig {
  /** Nivel de logging */
  level: 'debug' | 'info' | 'warn' | 'error';
  
  /** Incluir estadísticas detalladas en los logs */
  includeStats: boolean;
  
  /** Incluir contenido de chunks en logs (solo para debug) */
  includeContent: boolean;
  
  /** Función personalizada de logging */
  customLogger?: (level: string, message: string, data?: any) => void;
}
