import { prepararDocumentos } from './geminiClient';
import { PROMPT_MAPAS_MENTALES } from '@/config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Validar entrada
    if (!documentos || documentos.length === 0) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);
    const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
      ? resultadoDocumentos.content.join('\n\n')
      : resultadoDocumentos.content;

    if (!contenidoDocumentos || contenidoDocumentos.trim().length === 0) {
      throw new Error("El contenido de los documentos está vacío o no es válido.");
    }

    // Validar y limpiar instrucciones
    const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';

    // Construir el prompt final con validación
    let finalPrompt = PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);
    finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

    // Obtener configuración específica para mapas mentales
    const config = getOpenAIConfig('MAPAS_MENTALES');

    console.log(`🗺️ Generando mapa mental con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Generar el mapa mental usando OpenAI con la configuración correcta
    const messages = [{ role: 'user' as const, content: finalPrompt }];
    const responseText = await llamarOpenAI(messages, {
      ...config,
      activityName: 'Generación de Mapa Mental'
    });

    // Validar que la respuesta no esté vacía
    if (!responseText || responseText.trim().length === 0) {
      throw new Error("La IA no generó ningún contenido para el mapa mental.");
    }

    // Extraer y limpiar la respuesta (sin validaciones restrictivas)
    let htmlContent = responseText.trim();

    // Buscar el HTML en la respuesta (puede estar envuelto en markdown)
    const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
    if (htmlMatch) {
      htmlContent = htmlMatch[0];
    }

    // Limpiar marcadores de código markdown si existen
    htmlContent = htmlContent
      .replace(/```html/gi, '')
      .replace(/```/g, '')
      .trim();

    // Log para debugging - mostrar lo que generó la IA
    console.log('Contenido generado por la IA (primeros 500 caracteres):', htmlContent.substring(0, 500));
    console.log('Longitud total del contenido:', htmlContent.length);

    // Validar que el HTML sea válido
    if (!htmlContent.includes('<!DOCTYPE html>') || !htmlContent.includes('</html>')) {
      console.warn('⚠️ El contenido generado no parece ser HTML válido');
      console.log('Contenido completo:', htmlContent);
    } else {
      console.log('✅ HTML válido detectado');
    }

    // Retornar el contenido tal como lo generó la IA (sin validaciones)
    return htmlContent;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      throw new Error(`Error al generar el mapa mental: ${error.message}`);
    }

    throw new Error("Ha ocurrido un error inesperado al generar el mapa mental.");
  }
}
