/**
 * Servicio de Combinación de Resultados de Chunks
 * 
 * Este servicio se encarga de fusionar los resultados obtenidos del procesamiento
 * de múltiples chunks de un documento en un resultado coherente y unificado.
 */

import { 
  type ChunkCombinationResult, 
  type ResultCombinationConfig,
  type ChunkingContext 
} from '@/types/chunking';

import { 
  TEST_COMBINATION_CONFIG,
  FLASHCARD_COMBINATION_CONFIG,
  SUMMARY_COMBINATION_CONFIG 
} from '@/config/chunking';

/**
 * Interfaz para resultados de tests
 */
interface TestQuestion {
  pregunta: string;
  opciones: string[];
  respuesta_correcta: string;
  explicacion?: string;
}

/**
 * Interfaz para resultados de flashcards
 */
interface Flashcard {
  pregunta: string;
  respuesta: string;
}

/**
 * Clase base para combinadores de resultados
 */
abstract class BaseResultCombiner<T> {
  protected config: ResultCombinationConfig;

  constructor(config: ResultCombinationConfig) {
    this.config = config;
  }

  /**
   * Combina múltiples resultados en uno solo
   */
  abstract combineResults(results: T[], targetCount?: number): T[];

  /**
   * Crea el resultado final con metadatos
   */
  createCombinationResult(
    combinedResult: T[],
    individualResults: T[][],
    chunkingContexts: ChunkingContext[],
    sourceDocument: { id?: string; title: string; totalSize: number },
    processingTimeMs: number
  ): ChunkCombinationResult<T[]> {
    return {
      combinedResult,
      individualResults,
      combinationStats: {
        chunksProcessed: individualResults.length,
        duplicatesRemoved: this.calculateDuplicatesRemoved(individualResults, combinedResult),
        totalProcessingTimeMs: processingTimeMs,
        combinationStrategy: this.config.strategy
      },
      metadata: {
        chunkingConfig: chunkingContexts[0]?.chunkingStrategy ? 
          { maxChunkSize: 40000, overlapSize: 2000, sectionPatterns: [], fallbackToSentences: true } : 
          { maxChunkSize: 40000, overlapSize: 2000, sectionPatterns: [], fallbackToSentences: true },
        chunkingContext: chunkingContexts,
        sourceDocument
      }
    };
  }

  /**
   * Calcula el número de duplicados eliminados
   */
  private calculateDuplicatesRemoved(individualResults: T[][], combinedResult: T[]): number {
    const totalIndividual = individualResults.reduce((sum, results) => sum + results.length, 0);
    return Math.max(0, totalIndividual - combinedResult.length);
  }

  /**
   * Elimina duplicados usando la configuración
   */
  protected removeDuplicates(items: T[]): T[] {
    if (!this.config.deduplication) {
      return items;
    }

    const { isDuplicate, resolveDuplicate } = this.config.deduplication;
    const uniqueItems: T[] = [];

    for (const item of items) {
      const existingIndex = uniqueItems.findIndex(existing => isDuplicate(existing, item));
      
      if (existingIndex === -1) {
        uniqueItems.push(item);
      } else if (resolveDuplicate) {
        uniqueItems[existingIndex] = resolveDuplicate(uniqueItems[existingIndex], item);
      }
    }

    return uniqueItems;
  }

  /**
   * Aplica límite máximo de resultados
   */
  protected applyMaxResults(items: T[], maxResults?: number): T[] {
    const limit = maxResults || this.config.maxResults;
    if (limit && items.length > limit) {
      return items.slice(0, limit);
    }
    return items;
  }
}

/**
 * Combinador específico para resultados de tests
 */
export class TestResultCombiner extends BaseResultCombiner<TestQuestion> {
  constructor(config: ResultCombinationConfig = TEST_COMBINATION_CONFIG) {
    super(config);
  }

  combineResults(results: TestQuestion[][], targetCount?: number): TestQuestion[] {
    // Combinar todos los resultados
    const allQuestions = results.flat();
    
    // Eliminar duplicados
    const uniqueQuestions = this.removeDuplicates(allQuestions);
    
    // Aplicar límite si se especifica
    const finalQuestions = this.applyMaxResults(uniqueQuestions, targetCount);
    
    // Redistribuir respuestas correctas para mantener equilibrio
    return this.redistributeCorrectAnswers(finalQuestions, targetCount);
  }

  /**
   * Redistribuye las respuestas correctas para mantener equilibrio
   */
  private redistributeCorrectAnswers(questions: TestQuestion[], targetCount?: number): TestQuestion[] {
    if (!targetCount || questions.length <= targetCount) {
      return questions;
    }

    // Agrupar por respuesta correcta
    const groups = {
      a: questions.filter(q => q.respuesta_correcta === 'a'),
      b: questions.filter(q => q.respuesta_correcta === 'b'),
      c: questions.filter(q => q.respuesta_correcta === 'c'),
      d: questions.filter(q => q.respuesta_correcta === 'd')
    };

    const perGroup = Math.floor(targetCount / 4);
    const remainder = targetCount % 4;
    
    const result: TestQuestion[] = [];
    
    // Tomar preguntas de cada grupo
    ['a', 'b', 'c', 'd'].forEach((letter, index) => {
      const groupQuestions = groups[letter as keyof typeof groups];
      const takeCount = perGroup + (index < remainder ? 1 : 0);
      result.push(...groupQuestions.slice(0, takeCount));
    });

    // Si no tenemos suficientes, completar con las mejores preguntas restantes
    if (result.length < targetCount) {
      const remaining = questions.filter(q => !result.includes(q));
      result.push(...remaining.slice(0, targetCount - result.length));
    }

    return result;
  }

  /**
   * Método público para combinar resultados de tests
   */
  static combine(
    results: TestQuestion[][],
    targetCount: number,
    chunkingContexts: ChunkingContext[],
    sourceDocument: { id?: string; title: string; totalSize: number },
    processingTimeMs: number = 0
  ): ChunkCombinationResult<TestQuestion[]> {
    const combiner = new TestResultCombiner();
    const combinedResult = combiner.combineResults(results, targetCount);
    
    return combiner.createCombinationResult(
      combinedResult,
      results,
      chunkingContexts,
      sourceDocument,
      processingTimeMs
    );
  }
}

/**
 * Combinador específico para resultados de flashcards
 */
export class FlashcardResultCombiner extends BaseResultCombiner<Flashcard> {
  constructor(config: ResultCombinationConfig = FLASHCARD_COMBINATION_CONFIG) {
    super(config);
  }

  combineResults(results: Flashcard[][], targetCount?: number): Flashcard[] {
    // Combinar todos los resultados
    const allFlashcards = results.flat();
    
    // Eliminar duplicados
    const uniqueFlashcards = this.removeDuplicates(allFlashcards);
    
    // Aplicar límite
    return this.applyMaxResults(uniqueFlashcards, targetCount);
  }

  /**
   * Método público para combinar resultados de flashcards
   */
  static combine(
    results: Flashcard[][],
    targetCount: number,
    chunkingContexts: ChunkingContext[],
    sourceDocument: { id?: string; title: string; totalSize: number },
    processingTimeMs: number = 0
  ): ChunkCombinationResult<Flashcard[]> {
    const combiner = new FlashcardResultCombiner();
    const combinedResult = combiner.combineResults(results, targetCount);
    
    return combiner.createCombinationResult(
      combinedResult,
      results,
      chunkingContexts,
      sourceDocument,
      processingTimeMs
    );
  }
}

/**
 * Combinador específico para resultados de resúmenes
 */
export class SummaryResultCombiner extends BaseResultCombiner<string> {
  constructor(config: ResultCombinationConfig = SUMMARY_COMBINATION_CONFIG) {
    super(config);
  }

  combineResults(results: string[][]): string[] {
    // Para resúmenes, simplemente concatenamos los resultados
    const allSummaries = results.flat();
    
    // Unir todos los resúmenes en uno solo
    const combinedSummary = allSummaries.join('\n\n---\n\n');
    
    return [combinedSummary];
  }

  /**
   * Método público para combinar resultados de resúmenes
   */
  static combine(
    results: string[][],
    chunkingContexts: ChunkingContext[],
    sourceDocument: { id?: string; title: string; totalSize: number },
    processingTimeMs: number = 0
  ): ChunkCombinationResult<string[]> {
    const combiner = new SummaryResultCombiner();
    const combinedResult = combiner.combineResults(results);
    
    return combiner.createCombinationResult(
      combinedResult,
      results,
      chunkingContexts,
      sourceDocument,
      processingTimeMs
    );
  }
}

/**
 * Factory para crear combinadores según el tipo de resultado
 */
export class ResultCombinerFactory {
  static createTestCombiner(config?: Partial<ResultCombinationConfig>): TestResultCombiner {
    const finalConfig = { ...TEST_COMBINATION_CONFIG, ...config };
    return new TestResultCombiner(finalConfig);
  }

  static createFlashcardCombiner(config?: Partial<ResultCombinationConfig>): FlashcardResultCombiner {
    const finalConfig = { ...FLASHCARD_COMBINATION_CONFIG, ...config };
    return new FlashcardResultCombiner(finalConfig);
  }

  static createSummaryCombiner(config?: Partial<ResultCombinationConfig>): SummaryResultCombiner {
    const finalConfig = { ...SUMMARY_COMBINATION_CONFIG, ...config };
    return new SummaryResultCombiner(finalConfig);
  }
}

/**
 * Función de utilidad para combinar resultados automáticamente
 */
export function combineChunkResults<T>(
  results: T[][],
  type: 'tests' | 'flashcards' | 'summaries',
  options: {
    targetCount?: number;
    chunkingContexts: ChunkingContext[];
    sourceDocument: { id?: string; title: string; totalSize: number };
    processingTimeMs?: number;
    customConfig?: Partial<ResultCombinationConfig>;
  }
): ChunkCombinationResult<T[]> {
  const { targetCount, chunkingContexts, sourceDocument, processingTimeMs = 0, customConfig } = options;

  switch (type) {
    case 'tests':
      return TestResultCombiner.combine(
        results as TestQuestion[][],
        targetCount || 10,
        chunkingContexts,
        sourceDocument,
        processingTimeMs
      ) as ChunkCombinationResult<T[]>;

    case 'flashcards':
      return FlashcardResultCombiner.combine(
        results as Flashcard[][],
        targetCount || 10,
        chunkingContexts,
        sourceDocument,
        processingTimeMs
      ) as ChunkCombinationResult<T[]>;

    case 'summaries':
      return SummaryResultCombiner.combine(
        results as string[][],
        chunkingContexts,
        sourceDocument,
        processingTimeMs
      ) as ChunkCombinationResult<T[]>;

    default:
      throw new Error(`Tipo de resultado no soportado: ${type}`);
  }
}
